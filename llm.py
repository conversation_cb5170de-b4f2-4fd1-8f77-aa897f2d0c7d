from dotenv import load_dotenv
import os
import pandas as pd
import re
from prompts import PROMPT_TEMPLATE
from schemas import FinancialData
from openai import AzureOpenAI
from SECAPI import get_financial_doc
load_dotenv()

openai = AzureOpenAI(
    azure_endpoint = os.getenv("OPENAI_AZURE_ENDPOINT_EAST_US"),
    api_key = os.getenv("OPENAI_AZURE_KEY_EAST_US"),
    api_version = os.getenv("OPENAI_AZURE_API_VERSION_EAST_US")
)



def extract_information(text: str, current_period: pd.Period):
    data = openai.chat.completions.create(
        model="gpt-4o",
        messages=[
            {
                "role": "user", 
                "content": PROMPT_TEMPLATE.format(
                    current_period=f"Q{current_period.quarter} {current_period.year}", 
                    previous_year=f"Q{current_period.quarter} {current_period.year - 1}",
                    previous_quarter=f"Q{current_period.quarter - 1} {current_period.year}",
                    filings_text=text
                )
            }
        ],
        
    )
    clean_text = re.sub(
        r"^```(?:json)?\s*|\s*```$", "", 
        data.choices[0].message.content.strip(), 
        flags=re.DOTALL | re.IGNORECASE
    )
    out = FinancialData.model_validate_json(clean_text)
    return out
