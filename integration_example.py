from financial_metrics_validator import validate_financial_metrics, safe_process_metrics
import json

# Your example data
data = {
    "reported_quarter_revenue": 5870000000,
    "currency": "USD", 
    "reported_quarter_gross_margin": None,
    "reported_quarter_operating_margin": None,
    "next_quarter_revenue_growth_guidance": None,
    "next_quarter_operating_margin_guidance": 45.5,
    "next_quarter_gross_margin_guidance": None
}

# Validate the data
validated_metrics = validate_financial_metrics(data)
print(f"✓ Revenue: ${validated_metrics.reported_quarter_revenue:,}")
print(f"✓ Operating Margin Guidance: {validated_metrics.next_quarter_operating_margin_guidance}%")

# Integration into step2_extract_financial_metrics function:
def step2_extract_financial_metrics_with_validation(event_data):
    # ... existing code ...
    
    try:
        earnings_response = openai_service.get_completion_without_limits(
            prompt=extraction_prompt,
            temperature=0,
            response_format={"type": "json_object"},
        )
        
        # Parse and validate
        raw_metrics = json.loads(earnings_response)
        validated_metrics = validate_financial_metrics(raw_metrics)
        
        # Convert back to dict with all fields
        extracted_metrics = validated_metrics.dict()
        extracted_metrics["quarter"] = quarter
        extracted_metrics["year"] = year
        
        return extracted_metrics
        
    except Exception as e:
        logger.error(f"Validation failed: {e}")
        return {}