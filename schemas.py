from pydantic import BaseModel
from typing import Optional, TypedDict

class FinancialData(BaseModel):
    currency: Optional[str]
    reported_quarter_revenue: Optional[float]
    current_yoy_growth: Optional[float]
    reported_quarter_gross_margin: Optional[float]
    reported_quarter_operating_margin: Optional[float]
    next_quarter_revenue_growth_guidance: Optional[float]
    next_quarter_operating_margin_guidance: Optional[float]
    next_quarter_gross_margin_guidance: Optional[float]

class RevenueFigures(TypedDict):
    reported_quarter_revenue: Optional[float]
    current_yoy_growth: Optional[float]
    previous_yoy_growth: Optional[float]
    current_qoq_growth: Optional[float]
    previous_qoq_growth: Optional[float]

class GrossMarginFigures(TypedDict):
    reported_quarter_gross_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]

class OperatingMarginFigures(TypedDict):
    reported_quarter_operating_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]

class GuidanceFigures(TypedDict):
    next_quarter_revenue_growth_guidance: Optional[float]
    next_quarter_operating_margin_guidance: Optional[float]
    next_quarter_gross_margin_guidance: Optional[float]