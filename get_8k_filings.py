from sec_api import QueryApi, XbrlApi, RenderApi, PdfGeneratorApi
from dotenv import load_dotenv
from load_page import get_sec_filing_text
import os
from datetime import datetime

load_dotenv()

def search_8k_filings(ticker: str, search_date: datetime) -> str|None:
    queryApi = QueryApi(api_key=os.getenv("SEC_API_KEY"))
    date = search_date.strftime("%Y-%m-%d")
    query ={
        "query": f"ticker:{ticker} AND filedAt:[{date}T00:00:00 TO {date}T23:59:59]",
        "from": "0",
        "size": "20",
        "sort": [{ "filedAt": { "order": "desc" } }]
    }
    response = queryApi.get_filings(query)
    if not response:
        return None

    for doc in response["filings"][0]["documentFormatFiles"]:
        if doc["type"].startswith("EX-99"):
            url = doc["documentUrl"]
            print(url)
            text = get_sec_filing_text(url)
            return text