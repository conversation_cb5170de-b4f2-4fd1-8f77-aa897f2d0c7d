import requests
from bs4 import BeautifulSoup
import re

def get_sec_filing_text(url: str) -> str:
    """
    Fetches an SEC filing HTML page and returns cleaned text.
    
    Args:
        url (str): SEC filing URL.
        
    Returns:
        str: Cleaned filing text with boilerplate removed.
    """
    headers = {
        "User-Agent": "Mozilla/5.0 (compatible; MyApp/1.0; <EMAIL>)"
    }
    
    # Fetch HTML
    response = requests.get(url, headers=headers, timeout=30)
    response.raise_for_status()
    
    # Parse HTML
    soup = BeautifulSoup(response.text, "lxml")
    
    # Remove unwanted tags (scripts, styles, tables, images, etc.)
    for tag in soup(["script", "style", "table", "img", "noscript"]):
        tag.decompose()
    
    # Extract raw text
    text = soup.get_text(separator="\n")
    
    # Normalize whitespace
    text = re.sub(r"\n\s*\n+", "\n\n", text).strip()
    
    # Remove common SEC header/footer boilerplate
    boilerplate_patterns = [
        r"(?i)SECURITIES AND EXCHANGE COMMISSION.*?Washington, D\.C\..*?\n",
        r"(?i)End of Document.*"
    ]
    for pattern in boilerplate_patterns:
        text = re.sub(pattern, "", text, flags=re.DOTALL)
    
    return text


# Example usage
if __name__ == "__main__":
    url = "https://www.sec.gov/Archives/edgar/data/1373715/000137371525000274/erq2fy25.htm"
    filing_text = get_sec_filing_text(url)
    print(filing_text[:2000])  # Preview first 2000 characters