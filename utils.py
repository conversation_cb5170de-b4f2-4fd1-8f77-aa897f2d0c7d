import pandas as pd

def extract_quarter_and_year(event_title) -> pd.Period|None:
    quarter = None
    year = None
    if "Q1" in event_title:
        quarter = "Q1"
    elif "Q2" in event_title:
        quarter = "Q2"
    elif "Q3" in event_title:
        quarter = "Q3"
    elif "Q4" in event_title:
        quarter = "Q4"
    if "2029" in event_title:
        year = 2029
    elif "2028" in event_title:
        year = 2028
    elif "2027" in event_title:
        year = 2027
    elif "2026" in event_title:
        year = 2026
    elif "2025" in event_title:
        year = 2025
    elif "2024" in event_title:
        year = 2024
    elif "2023" in event_title:
        year = 2023
    
    if quarter and year:
        return pd.Period(f"{year}{quarter}")
    return None