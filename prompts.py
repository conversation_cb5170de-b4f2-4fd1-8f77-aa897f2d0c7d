PROMPT_TEMPLATE = """
You are an expert financial analyst AI assistant. 
Your task is to analyze the provided earnings filing text and extract the following financial metrics 
for current quarter {current_period}.

**Key Information to Extract:**

1.  **reported_quarter_revenue**: The total revenue reported for {current_period}.
2.  **currency**: The currency code in which the revenue is reported (e.g., "USD", "INR", "EUR", "RMB").
3.  **current_yoy_growth**: The year-over-year growth percentage for {current_period}.
4.  **reported_quarter_gross_margin**: The gross margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 45.2 instead of "45.2%").
5.  **reported_quarter_operating_margin**: The operating margin for the most recently completed quarter. Extract as a numerical value WITHOUT the % symbol (e.g., 15.7 instead of "15.7%").
6.  **next_quarter_revenue_growth_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) revenue growth for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol. If provided as a specific amount, convert to the exact number.
7.  **next_quarter_operating_margin_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) operating margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 16.5 instead of "16.5%").
8.  **next_quarter_gross_margin_guidance**: The management's forecasted Quarter-over-Quarter (QoQ) gross margin for the upcoming quarter. If provided as a percentage, extract as a numerical value WITHOUT the % symbol (e.g., 12.3 instead of "12.3%").

You MUST return your answer strictly as a JSON object with the following structure and keys exactly as shown:
{{
    "reported_quarter_revenue": number | null,
    "currency": "string | null"
    "current_yoy_growth": number | null,
    "reported_quarter_gross_margin": number | null,
    "reported_quarter_operating_margin": number | null,
    "next_quarter_revenue_growth_guidance": number | null,
    "next_quarter_operating_margin_guidance": number | null,
    "next_quarter_gross_margin_guidance": number | null
}}

**Instructions for Extraction:**

- Focus SOLELY on the information present in the filings text provided. Do not infer information from external knowledge.
- For revenue, extract the numerical value and convert it to a precise number (e.g., convert $1.2B to 1200000000, €500M to 500000000).
- Extract the currency code (USD, EUR, INR, RMB, etc.) separately and include it in the "currency" field.
- For all percentage values, remove the % symbol and provide only the numerical value (e.g., 45.2 instead of "45.2%").
- For revenue and margins, prioritize figures explicitly stated for the "reported quarter" or "current quarter" being discussed.
- For guidance, look for forward-looking statements about the "next quarter," "upcoming quarter," or specific future period.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.
- Always give out a single number if available even by inference. For example, "low to mid-single digits" can be assumed as 4% and "45.5 to 46.5" can be assumed as 46%.
- If guidance is provided as a qualitative statement (e.g., "low single-digits") that cannot be converted to a precise number, keep it as a string.


**Filings Text to Analyze:**

--- BEGIN filings TEXT ---
{filings_text}
--- END filings TEXT ---

Now, please process the filings text provided above and return the JSON output
"""