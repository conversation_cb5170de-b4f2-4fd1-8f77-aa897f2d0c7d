from sec_api import Query<PERSON>pi, XbrlApi, RenderApi, PdfGeneratorApi
from dotenv import load_dotenv
from load_page import get_sec_filing_text
import os

load_dotenv()

SEC_API_KEY = os.getenv('SEC_API_KEY')

def search_8k(ticker):
    queryApi = QueryApi(api_key=SEC_API_KEY)

    query = {
        "query": "formType:\"8-K\" AND items:\"9.01\"",
        "from": "0",
        "size": "20",
        "sort": [{ "filedAt": { "order": "desc" } }]
    }

    # Execute the query to find the filing
    response = queryApi.get_filings(query)

    return response

def download_8k_pdf(url_8k):
    pdfGeneratorApi = PdfGeneratorApi(SEC_API_KEY)
    # filing_8k = renderApi.get_filing(url_8k)
    file_pdf = pdfGeneratorApi.get_pdf(url_8k)
    with open('8k.pdf', 'wb') as f:
        f.write(file_pdf)
    return None

def get_financial_doc(ticker):
    queryApi = QueryApi(api_key=SEC_API_KEY)
    query = {
        "query": f"formType:\"8-K\" AND documentFormatFiles.type:*99* AND ticker:{ticker}",
        "from": "0",
        "size": "1",
        "sort": [{ "filedAt": { "order": "desc" } }]
    }
    response = queryApi.get_filings(query)
    for doc in response["filings"][0]["documentFormatFiles"]:
        if doc["type"] == "EX-99.1":
            url = doc["documentUrl"]
    print(url)
    text = get_sec_filing_text(url)
    return text

if __name__ == "__main__":
    # print(get_quarterly_revenue('MSFT'))
    # print(download_8k_pdf('https://www.sec.gov/Archives/edgar/data/1833764/000089924321006812/xslF345X02/doc3.xml'))
    text = get_financial_doc('ADBE')