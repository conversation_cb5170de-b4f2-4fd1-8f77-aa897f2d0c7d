from schemas import (
    FinancialData,
    RevenueFigures,
    GrossMarginFigures,
    OperatingMarginFigures,
)
from typing import Optional, <PERSON><PERSON>


def calculate_financial_data(
    data: FinancialData,
    previous_quarter_data: Optional[dict],
    previous_year_data: Optional[dict],
) -> Tuple[RevenueFigures, GrossMarginFigures, OperatingMarginFigures]:
    
    revenue = RevenueFigures(
        reported_quarter_revenue=data.reported_quarter_revenue,
        current_yoy_growth=data.current_yoy_growth,
        previous_yoy_growth=previous_year_data.get("current_yoy_growth") if previous_year_data else None,
        current_qoq_growth=None,
        previous_qoq_growth=previous_quarter_data.get("current_qoq_growth") if previous_quarter_data else None,
    )

    # calculating revenue figures
    if revenue["reported_quarter_revenue"] is not None:
        if not revenue["current_yoy_growth"] and previous_year_data:
            revenue["current_yoy_growth"] = calculate_growth(previous_year_data.get("reported_quarter_revenue"), revenue["reported_quarter_revenue"])
        if not revenue["current_qoq_growth"] and previous_quarter_data:
            revenue["current_qoq_growth"] = calculate_growth(previous_quarter_data.get("reported_quarter_revenue"), revenue["reported_quarter_revenue"])

    gross_margin = GrossMarginFigures(
        reported_quarter_gross_margin=data.reported_quarter_gross_margin,
        yoy_margin_change=None,
        qoq_margin_change=None
    )

    # calculating gross margin figures
    if gross_margin["reported_quarter_gross_margin"] is not None:
        if not gross_margin["yoy_margin_change"] and previous_year_data:
            gross_margin["yoy_margin_change"] = calculate_growth(previous_year_data.get("reported_quarter_gross_margin"), gross_margin["reported_quarter_gross_margin"])
        if not gross_margin["qoq_margin_change"] and previous_quarter_data:
            gross_margin["qoq_margin_change"] = calculate_growth(previous_quarter_data.get("reported_quarter_gross_margin"), gross_margin["reported_quarter_gross_margin"])

    operating_margin = OperatingMarginFigures(
        reported_quarter_operating_margin=data.reported_quarter_operating_margin,
        yoy_margin_change=(data.reported_quarter_operating_margin - previous_year_data['reported_quarter_operating_margin']) if previous_year_data else None,
        qoq_margin_change=(data.reported_quarter_operating_margin - previous_quarter_data['reported_quarter_operating_margin']) if previous_quarter_data else None
    )

    # calculating operating margin figures
    if operating_margin["reported_quarter_operating_margin"] is not None:
        if not operating_margin["yoy_margin_change"] and previous_year_data:
            operating_margin["yoy_margin_change"] = calculate_growth(previous_year_data.get("reported_quarter_operating_margin"), operating_margin["reported_quarter_operating_margin"])
        if not operating_margin["qoq_margin_change"] and previous_quarter_data:
            operating_margin["qoq_margin_change"] = calculate_growth(previous_quarter_data.get("reported_quarter_operating_margin"), operating_margin["reported_quarter_operating_margin"])

    return revenue, gross_margin, operating_margin


def calculate_growth(previous_value: Optional[float], current_value: Optional[float]) -> Optional[float]:
    if previous_value is None or current_value is None:
        return None
    return ((current_value / previous_value) - 1) * 100 if previous_value != 0 else None
