import pandas as pd
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId
import os
load_dotenv()
from utils import extract_quarter_and_year
from get_8k_filings import search_8k_filings
from llm import extract_information
from calculations import calculate_financial_data
from generate_analysis import generate_revenue_analysis, generate_gross_margin_analysis, generate_operating_margin_analysis

client = MongoClient(os.getenv('MONGO_URI'))
db = client['slated_prod9_test']
events_collection = db['public_investor_events']
earnings_collection = db['earnings_analysis']

def create_revenue_message(event_id, ticker):
    event = events_collection.find_one({"_id": ObjectId(event_id)})
    if not event:
        print("Event not found")
        return None

    current_period = extract_quarter_and_year(event['title'])
    if not current_period:
        print("Current period not found")
        return None

    previous_quarter_earnings = earnings_collection.find_one({
        "ticker": ticker,
        "quarter": f"Q{current_period.quarter - 1}",
        "year": current_period.year
    })

    previous_year_earnings = earnings_collection.find_one({
        "ticker": ticker,
        "quarter": f"Q{current_period.quarter}",
        "year": current_period.year - 1
    })

    current_filings = search_8k_filings(ticker, event['date'])
    if not current_filings:
        print("Current filings not found")
        return None
    
    current_data = extract_information(current_filings, current_period)
    if not current_data.reported_quarter_revenue:
        print("Current reported quarter revenue not found")
        return None
    
    print(current_data)

    revenue_figures, gross_margin_figures, operating_margin_figures = calculate_financial_data(
        current_data, previous_quarter_earnings, previous_year_earnings
    )
    
    full_message = ""
    revenue_analysis = generate_revenue_analysis(current_period, revenue_figures) 
    if revenue_analysis: full_message += revenue_analysis + " "
    gross_margin_analysis = generate_gross_margin_analysis(current_period, gross_margin_figures)
    if gross_margin_analysis: full_message += gross_margin_analysis + " "
    operating_margin_analysis = generate_operating_margin_analysis(current_period, operating_margin_figures)
    if operating_margin_analysis: full_message += operating_margin_analysis + " "
    
    return full_message.strip()

if __name__ == "__main__":
    event_id = "687901ed79d26cf8aeace65d"
    ticker = "PEP"
    print(create_revenue_message(event_id, ticker))