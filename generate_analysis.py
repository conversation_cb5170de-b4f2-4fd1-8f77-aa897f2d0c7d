import pandas as pd
from typing import TypedDict
from schemas import RevenueFigures, GrossMarginFigures, OperatingMarginFigures, GuidanceFigures

def generate_revenue_analysis(period: pd.Period, data: RevenueFigures) -> str|None:
    
    if data["current_yoy_growth"] and data["previous_yoy_growth"] and data["current_qoq_growth"] and data["previous_qoq_growth"]:
        message = f'Revenue: In Q{period.quarter}-{period.year}, revenue increased {data["current_yoy_growth"]}% year-over-year (YoY), '\
        f'compared to {data["previous_yoy_growth"]}% growth in Q{period.quarter}-{period.year - 1}. '\
        f'Quarter-over-quarter (QoQ), revenue grew {data["current_qoq_growth"]}%, versus {data["previous_qoq_growth"]}% in Q{period.quarter - 1}-{period.year}.'

    elif data["current_yoy_growth"] and data["previous_yoy_growth"] and data["current_qoq_growth"]:
        message = f'Revenue: In Q{period.quarter}-{period.year}, revenue increased {data["current_yoy_growth"]}% year-over-year (YoY), '\
        f'compared to {data["previous_yoy_growth"]}% growth in Q{period.quarter}-{period.year - 1}. '\
        f'Quarter-over-quarter (QoQ), revenue grew {data["current_qoq_growth"]}%.'
    
    elif data["current_yoy_growth"] and data["previous_yoy_growth"]:
        message = f'Revenue: In Q{period.quarter}-{period.year}, revenue increased {data["current_yoy_growth"]}% year-over-year (YoY), '\
        f'compared to {data["previous_yoy_growth"]}% growth in Q{period.quarter}-{period.year - 1}.'
    
    elif data["current_yoy_growth"]:
        message = f'Revenue: In Q{period.quarter}-{period.year}, revenue increased {data["current_yoy_growth"]}% year-over-year (YoY).'
    
    else:
        message = None
    
    return message


def generate_gross_margin_analysis(period: pd.Period, data: GrossMarginFigures) -> str|None:

    if data.reported_quarter_gross_margin and data.yoy_margin_change and data.qoq_margin_change:
        message = f"Gross Margins: Gross margins for Q{period.quarter}-{period.year} were {data.reported_quarter_gross_margin}%, "\
            f"representing a {data.yoy_margin_change} basis points (bps) change from Q{period.quarter}-{period.year - 1}, "\
            f"and a {data.qoq_margin_change} bps change from Q{period.quarter - 1}-{period.year}."
        
    elif data.reported_quarter_gross_margin and data.yoy_margin_change:
        message = f"Gross Margins: Gross margins for Q{period.quarter}-{period.year} were {data.reported_quarter_gross_margin}%, "\
            f"representing a {data.yoy_margin_change} basis points (bps) change from Q{period.quarter}-{period.year - 1}."
    
    elif data.reported_quarter_gross_margin and data.qoq_margin_change:
        message = f"Gross Margins: Gross margins for Q{period.quarter}-{period.year} were {data.reported_quarter_gross_margin}%, "\
            f"representing a {data.qoq_margin_change} basis points (bps) change from Q{period.quarter - 1}-{period.year}."
    
    elif data.reported_quarter_gross_margin:
        message = f"Gross Margins: Gross margins for Q{period.quarter}-{period.year} were {data.reported_quarter_gross_margin}%."
    
    else:
        message = None
    
    return message


def generate_operating_margin_analysis(period: pd.Period, data: OperatingMarginFigures) -> str|None:

    if data.reported_quarter_operating_margin:
        message = f"Operating Margins: Operating margins stood at {data.reported_quarter_operating_margin}% in Q{period.quarter}-{period.year}, "\
                f"a {data.yoy_margin_change} bps change compared to Q{period.quarter}-{period.year - 1}, "\
                f"and a {data.qoq_margin_change} bps change versus Q{period.quarter - 1}-{period.year}."
    else:
        message = None
    
    return message


# def generate_guidance_analysis(period: pd.Period, data: GuidanceFigures) -> str|None:
#     if data.next_quarter_revenue_growth_guidance and data.next_quarter_operating_margin_guidance and data.next_quarter_gross_margin_guidance:
#         message = f"Guidance: For Q{period.quarter + 1}-{period.year}, revenue is expected to grow {data.next_quarter_revenue_growth_guidance}% YoY, "\
#                 f"implying a _% deceleration from {quarter}-{year}’s YoY growth. "\
#                 f"This compares to a _% deceleration guided when {previous_quarter} results were reported. "\
#                 f"Operating margins for {next_quarter} are guided at _%, a _ bps change from {quarter}-{year}."
